<template>
    <Head title="Organisation name">
        <meta name="description" content="Add your organisation name to your Pravi account." />
    </Head>

    <OnboardingLayout :currentStep="{ step: 1, sub: 1 }">
        <div>
            <p>
                First things first. Let's get information about your
                organisation so we can craft personalised campaigns
            </p>

            <h1 class="fs-2xl font-bold after:content-['*'] after:text-text-action">
                What's the name of your organisation?
            </h1>

            <form @submit.prevent="submit">
                <TextInput id="name" v-model="form.name" name="name" label="Organisation name" type="text" required
                    :rules="[rules.required]" :serverError="form.errors.name" class="col-span-2"></TextInput>

                <div class="">
                    <button type="submit" class="btn--secondary group ml-auto"
                        :disabled="form.processing || !form.name">
                        Continue

                        <IconArrowRight class="stroke-text-action-hover group-disabled:stroke-text-disabled" />
                    </button>
                </div>
            </form>
        </div>

        <template v-slot:image>
            <img
                class="h-auto w-auto max-w-[400px] lg:max-h-full xl:-translate-x-64"
                src="/images/onboarding/onboarding-1.png"
                alt=""
                width="525"
                height="800"
            />
        </template>
    </OnboardingLayout>
</template>

<script setup>
import { Head, useForm, router } from "@inertiajs/vue3";
import TextInput from "@/Components/TextInput/TextInput.vue";
import OnboardingLayout from "@/Layouts/OnboardingLayout.vue";
import IconArrowRight from "@/Components/Icons/IconArrowRight.vue";
import rules from "@/utilities/validation-rules";

const { company } = defineProps({
    company: Object,
});

const form = useForm({
    name: company?.name ?? "",
});

const submit = () => {
    form.post(route("api.setup.company"), {
        onSuccess: () => {
            router.get("/setup/organisation-mission");
        },
    });
};
</script>
